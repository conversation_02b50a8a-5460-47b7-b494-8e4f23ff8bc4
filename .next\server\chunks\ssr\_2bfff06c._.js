module.exports = [
"[project]/app/aeonikbold_b98b5370.module.css [app-rsc] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "className": "aeonikbold_b98b5370-module__HbNZGa__className",
  "variable": "aeonikbold_b98b5370-module__HbNZGa__variable",
});
}),
"[project]/app/aeonikbold_b98b5370.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$aeonikbold_b98b5370$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/app/aeonikbold_b98b5370.module.css [app-rsc] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$aeonikbold_b98b5370$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'aeonikBold', 'aeonikBold Fallback'"
    }
};
if (__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$aeonikbold_b98b5370$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$aeonikbold_b98b5370$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}),
"[project]/app/aeonikregular_756171f0.module.css [app-rsc] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "className": "aeonikregular_756171f0-module__It3Qua__className",
  "variable": "aeonikregular_756171f0-module__It3Qua__variable",
});
}),
"[project]/app/aeonikregular_756171f0.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$aeonikregular_756171f0$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/app/aeonikregular_756171f0.module.css [app-rsc] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$aeonikregular_756171f0$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'aeonikRegular', 'aeonikRegular Fallback'"
    }
};
if (__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$aeonikregular_756171f0$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$aeonikregular_756171f0$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}),
"[project]/app/layout.tsx [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>RootLayout,
    "metadata",
    ()=>metadata
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$mono$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/mono.js [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistmono_157ca88a$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__GeistMono$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistmono_157ca88a.js [app-rsc] (ecmascript) <export default as GeistMono>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$sans$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/sans.js [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistsans_d5a4f12f$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__GeistSans$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistsans_d5a4f12f.js [app-rsc] (ecmascript) <export default as GeistSans>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$aeonikbold_b98b5370$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/aeonikbold_b98b5370.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$aeonikregular_756171f0$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/aeonikregular_756171f0.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
const metadata = {
    title: "Create Next App",
    description: "Generated by create next app"
};
function RootLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistsans_d5a4f12f$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__GeistSans$3e$__["GeistSans"].variable} ${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistmono_157ca88a$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__GeistMono$3e$__["GeistMono"].variable} ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$aeonikbold_b98b5370$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].variable} ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$aeonikregular_756171f0$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].variable}`,
        lang: "en",
        suppressHydrationWarning: true,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("head", {}, void 0, false, {
                fileName: "[project]/app/layout.tsx",
                lineNumber: 31,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
                children: children
            }, void 0, false, {
                fileName: "[project]/app/layout.tsx",
                lineNumber: 32,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/layout.tsx",
        lineNumber: 26,
        columnNumber: 5
    }, this);
}
}),
"[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

module.exports = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-rsc] (ecmascript)").vendored['react-rsc'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}),
"[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistmono_157ca88a.module.css [app-rsc] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "className": "geistmono_157ca88a-module__WVqpCG__className",
  "variable": "geistmono_157ca88a-module__WVqpCG__variable",
});
}),
"[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistmono_157ca88a.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistmono_157ca88a$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistmono_157ca88a.module.css [app-rsc] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistmono_157ca88a$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'GeistMono', ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace"
    }
};
if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistmono_157ca88a$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistmono_157ca88a$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}),
"[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/mono.js [app-rsc] (ecmascript) <locals>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistmono_157ca88a$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistmono_157ca88a.js [app-rsc] (ecmascript)");
;
;
}),
"[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistmono_157ca88a.js [app-rsc] (ecmascript) <export default as GeistMono>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GeistMono",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistmono_157ca88a$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"]
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistmono_157ca88a$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistmono_157ca88a.js [app-rsc] (ecmascript)");
}),
"[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistsans_d5a4f12f.module.css [app-rsc] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "className": "geistsans_d5a4f12f-module__tBZO7G__className",
  "variable": "geistsans_d5a4f12f-module__tBZO7G__variable",
});
}),
"[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistsans_d5a4f12f.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistsans_d5a4f12f$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistsans_d5a4f12f.module.css [app-rsc] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistsans_d5a4f12f$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'GeistSans', 'GeistSans Fallback'"
    }
};
if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistsans_d5a4f12f$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistsans_d5a4f12f$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}),
"[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/sans.js [app-rsc] (ecmascript) <locals>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistsans_d5a4f12f$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistsans_d5a4f12f.js [app-rsc] (ecmascript)");
;
;
}),
"[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistsans_d5a4f12f.js [app-rsc] (ecmascript) <export default as GeistSans>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GeistSans",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistsans_d5a4f12f$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"]
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$geist$40$1$2e$4$2e$2_next$40$15$2e$5$2e$2_rea_62f3a0f29926db72aebc49356c7913ca$2f$node_modules$2f$geist$2f$dist$2f$geistsans_d5a4f12f$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistsans_d5a4f12f.js [app-rsc] (ecmascript)");
}),
];

//# sourceMappingURL=_2bfff06c._.js.map