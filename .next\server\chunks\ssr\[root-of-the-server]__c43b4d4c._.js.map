{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/app/%28root%29/page.tsx"], "sourcesContent": ["export default function Home() {\n  return (\n    <div className=\"flex flex-col gap-6 py-20 lg:py-48\">\n      <div className=\"font-sans\">\n        <h2 className=\"font-bold\">Sans</h2>\n        <p>\n          Are you looking for inspiration to create a stunning portfolio website\n          that stands out in the given landing page examples? Crafting a\n          captivating online portfolio can be an intimidating task, especially\n          when you're trying to find the right template. There is a way to\n          simplify the process and find a NextJS portfolio template tailored to\n          your needs. Imagine having a solution that allows you to jumpstart\n          your digital presence and showcase your skills seamlessly.\n        </p>\n      </div>\n      <div className=\"font-mono\">\n        <h2 className=\"font-bold\">Mono</h2>\n        <p>\n          Are you looking for inspiration to create a stunning portfolio website\n          that stands out in the given landing page examples? Crafting a\n          captivating online portfolio can be an intimidating task, especially\n          when you're trying to find the right template. There is a way to\n          simplify the process and find a NextJS portfolio template tailored to\n          your needs. Imagine having a solution that allows you to jumpstart\n          your digital presence and showcase your skills seamlessly.\n        </p>\n      </div>\n      <div className=\"font-aeonik\">\n        <h2 className=\"font-bold\">Aeonik</h2>\n        <p>\n          Are you looking for inspiration to create a stunning portfolio website\n          that stands out in the given landing page examples? Crafting a\n          captivating online portfolio can be an intimidating task, especially\n          when you're trying to find the right template. There is a way to\n          simplify the process and find a NextJS portfolio template tailored to\n          your needs. Imagine having a solution that allows you to jumpstart\n          your digital presence and showcase your skills seamlessly.\n        </p>\n      </div>\n      <div className=\"font-aeonik-bold\">\n        <h2 className=\"font-bold\">Aeonik Bold</h2>\n        <p>\n          Are you looking for inspiration to create a stunning portfolio website\n          that stands out in the given landing page examples? Crafting a\n          captivating online portfolio can be an intimidating task, especially\n          when you're trying to find the right template. There is a way to\n          simplify the process and find a NextJS portfolio template tailored to\n          your needs. Imagine having a solution that allows you to jumpstart\n          your digital presence and showcase your skills seamlessly.\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAAY;;;;;;kCAC1B,6WAAC;kCAAE;;;;;;;;;;;;0BAUL,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAAY;;;;;;kCAC1B,6WAAC;kCAAE;;;;;;;;;;;;0BAUL,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAAY;;;;;;kCAC1B,6WAAC;kCAAE;;;;;;;;;;;;0BAUL,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAAY;;;;;;kCAC1B,6WAAC;kCAAE;;;;;;;;;;;;;;;;;;AAYX", "debugId": null}}]}