{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,uOAAO,EAAC,IAAA,iMAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,qPAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,gTAAI,GAAG;IAE9B,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/navigation-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as NavigationMenuPrimitive from \"@radix-ui/react-navigation-menu\"\nimport { cva } from \"class-variance-authority\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction NavigationMenu({\n  className,\n  children,\n  viewport = true,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Root> & {\n  viewport?: boolean\n}) {\n  return (\n    <NavigationMenuPrimitive.Root\n      data-slot=\"navigation-menu\"\n      data-viewport={viewport}\n      className={cn(\n        \"group/navigation-menu relative flex max-w-max flex-1 items-center justify-center\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      {viewport && <NavigationMenuViewport />}\n    </NavigationMenuPrimitive.Root>\n  )\n}\n\nfunction NavigationMenuList({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.List>) {\n  return (\n    <NavigationMenuPrimitive.List\n      data-slot=\"navigation-menu-list\"\n      className={cn(\n        \"group flex flex-1 list-none items-center justify-center gap-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Item>) {\n  return (\n    <NavigationMenuPrimitive.Item\n      data-slot=\"navigation-menu-item\"\n      className={cn(\"relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1\"\n)\n\nfunction NavigationMenuTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Trigger>) {\n  return (\n    <NavigationMenuPrimitive.Trigger\n      data-slot=\"navigation-menu-trigger\"\n      className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n      {...props}\n    >\n      {children}{\" \"}\n      <ChevronDownIcon\n        className=\"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180\"\n        aria-hidden=\"true\"\n      />\n    </NavigationMenuPrimitive.Trigger>\n  )\n}\n\nfunction NavigationMenuContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Content>) {\n  return (\n    <NavigationMenuPrimitive.Content\n      data-slot=\"navigation-menu-content\"\n      className={cn(\n        \"data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto\",\n        \"group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuViewport({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Viewport>) {\n  return (\n    <div\n      className={cn(\n        \"absolute top-full left-0 isolate z-50 flex justify-center\"\n      )}\n    >\n      <NavigationMenuPrimitive.Viewport\n        data-slot=\"navigation-menu-viewport\"\n        className={cn(\n          \"origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction NavigationMenuLink({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Link>) {\n  return (\n    <NavigationMenuPrimitive.Link\n      data-slot=\"navigation-menu-link\"\n      className={cn(\n        \"data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuIndicator({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Indicator>) {\n  return (\n    <NavigationMenuPrimitive.Indicator\n      data-slot=\"navigation-menu-indicator\"\n      className={cn(\n        \"data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"bg-border relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm shadow-md\" />\n    </NavigationMenuPrimitive.Indicator>\n  )\n}\n\nexport {\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n  navigationMenuTriggerStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AAEA;;;;;;AAEA,SAAS,eAAe,KAOvB;QAPuB,EACtB,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,GAAG,OAGJ,GAPuB;IAQtB,qBACE,4TAAC,gSAA4B;QAC3B,aAAU;QACV,iBAAe;QACf,WAAW,IAAA,qHAAE,EACX,oFACA;QAED,GAAG,KAAK;;YAER;YACA,0BAAY,4TAAC;;;;;;;;;;;AAGpB;KAtBS;AAwBT,SAAS,mBAAmB,KAGgC;QAHhC,EAC1B,SAAS,EACT,GAAG,OACuD,GAHhC;IAI1B,qBACE,4TAAC,gSAA4B;QAC3B,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,iEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,KAGgC;QAHhC,EAC1B,SAAS,EACT,GAAG,OACuD,GAHhC;IAI1B,qBACE,4TAAC,gSAA4B;QAC3B,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,MAAM,6BAA6B,IAAA,qPAAG,EACpC;AAGF,SAAS,sBAAsB,KAIgC;QAJhC,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D,GAJhC;IAK7B,qBACE,4TAAC,mSAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,8BAA8B,SAAS;QACpD,GAAG,KAAK;;YAER;YAAU;0BACX,4TAAC,kUAAe;gBACd,WAAU;gBACV,eAAY;;;;;;;;;;;;AAIpB;MAlBS;AAoBT,SAAS,sBAAsB,KAGgC;QAHhC,EAC7B,SAAS,EACT,GAAG,OAC0D,GAHhC;IAI7B,qBACE,4TAAC,mSAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,oWACA,6hCACA;QAED,GAAG,KAAK;;;;;;AAGf;MAfS;AAiBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,4TAAC;QACC,WAAW,IAAA,qHAAE,EACX;kBAGF,cAAA,4TAAC,oSAAgC;YAC/B,aAAU;YACV,WAAW,IAAA,qHAAE,EACX,sVACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,mBAAmB,KAGgC;QAHhC,EAC1B,SAAS,EACT,GAAG,OACuD,GAHhC;IAI1B,qBACE,4TAAC,gSAA4B;QAC3B,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,ydACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,wBAAwB,KAGgC;QAHhC,EAC/B,SAAS,EACT,GAAG,OAC4D,GAHhC;IAI/B,qBACE,4TAAC,qSAAiC;QAChC,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,gMACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;MAhBS", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/config/docs.ts"], "sourcesContent": ["import {\r\n  ChartNoAxesColumnDecreasingIcon,\r\n  FileTextIcon,\r\n  GlobeIcon,\r\n  HouseIcon,\r\n  ImageIcon,\r\n  Mailbox,\r\n  MountainIcon,\r\n  NotebookPenIcon,\r\n  UsersIcon,\r\n} from \"lucide-react\";\r\nimport { FaEarthAfrica, FaEarthAmericas, FaEarthEurope } from \"react-icons/fa6\";\r\nimport type { TFooterLink, TNavItem, TNewsLink } from \"@/types\";\r\n\r\nexport const navItems: TNavItem[] = [\r\n  {\r\n    href: \"/pricing\",\r\n    label: \"Pricing\",\r\n    icon: HouseIcon,\r\n  },\r\n  {\r\n    href: \"/about\",\r\n    label: \"About\",\r\n    icon: NotebookPenIcon,\r\n  },\r\n  {\r\n    href: \"/docs\",\r\n    label: \"Docs\",\r\n    icon: UsersIcon,\r\n  },\r\n  {\r\n    href: \"/privacy\",\r\n    label: \"Privacy\",\r\n    icon: ImageIcon,\r\n  },\r\n];\r\nexport const newsLinks: TNewsLink[] = [\r\n  {\r\n    title: \"News\",\r\n    links: [\r\n      {\r\n        title: \"Breaking News\",\r\n        href: \"/\",\r\n        icon: Mailbox,\r\n        external: false,\r\n      },\r\n      {\r\n        title: \"Latest News\",\r\n        href: \"/\",\r\n        icon: FileTextIcon,\r\n        external: false,\r\n      },\r\n      {\r\n        title: \"Top Stories\",\r\n        href: \"/\",\r\n        icon: ChartNoAxesColumnDecreasingIcon,\r\n        external: false,\r\n      },\r\n      {\r\n        title: \"Local News\",\r\n        href: \"/\",\r\n        icon: GlobeIcon,\r\n        external: false,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Region\",\r\n    links: [\r\n      {\r\n        title: \"Rwanda\",\r\n        href: \"/\",\r\n        external: false,\r\n        icon: MountainIcon,\r\n      },\r\n      {\r\n        title: \"Africa\",\r\n        href: \"/\",\r\n        external: false,\r\n        icon: FaEarthAfrica,\r\n      },\r\n      {\r\n        title: \"Usa\",\r\n        href: \"/\",\r\n        external: false,\r\n        icon: FaEarthAmericas,\r\n      },\r\n      {\r\n        title: \"Europe\",\r\n        href: \"/\",\r\n        external: false,\r\n        icon: FaEarthEurope,\r\n      },\r\n    ],\r\n  },\r\n];\r\nexport const footerLinks: TFooterLink[] = [\r\n  {\r\n    title: \"Newsroom\",\r\n    links: [\r\n      { name: \"Latest News\", href: \"/\", external: false },\r\n      { name: \"Top Stories\", href: \"/\", external: false },\r\n      { name: \"Editor's Picks\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Company\",\r\n    links: [\r\n      { name: \"About Us\", href: \"/\", external: false },\r\n      { name: \"Careers\", href: \"/\", external: false },\r\n      { name: \"Press\", href: \"/\", external: false },\r\n      { name: \"Contact\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n  {\r\n    title: \"For Business\",\r\n    links: [\r\n      { name: \"Advertise with Us\", href: \"/\", external: false },\r\n      { name: \"Media Kit\", href: \"/\", external: false },\r\n      { name: \"Partner with Us\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n  {\r\n    title: \"More\",\r\n    links: [\r\n      { name: \"Newsletter\", href: \"/\", external: false },\r\n      { name: \"Mobile App\", href: \"/\", external: false },\r\n      { name: \"RSS Feeds\", href: \"/\", external: false },\r\n      { name: \"Help Center\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Terms & Policies\",\r\n    links: [\r\n      { name: \"Terms of Use\", href: \"/\", external: false },\r\n      { name: \"Privacy Policy\", href: \"/\", external: false },\r\n      { name: \"Cookie Policy\", href: \"/\", external: false },\r\n      { name: \"Editorial Policy\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Safety\",\r\n    links: [\r\n      { name: \"Fact-Checking\", href: \"/\", external: false },\r\n      { name: \"Corrections\", href: \"/\", external: false },\r\n      { name: \"Trust & Transparency\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Follow Us\",\r\n    links: [\r\n      { name: \"Facebook\", href: \"/\", external: true },\r\n      { name: \"Twitter\", href: \"/\", external: true },\r\n      { name: \"Instagram\", href: \"/\", external: true },\r\n      { name: \"YouTube\", href: \"/\", external: true },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Sections\",\r\n    links: [\r\n      { name: \"Politics\", href: \"/\", external: false },\r\n      { name: \"Business\", href: \"/\", external: false },\r\n      { name: \"Technology\", href: \"/\", external: false },\r\n      { name: \"Health\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Resources\",\r\n    links: [\r\n      { name: \"Media Resources\", href: \"/\", external: false },\r\n      { name: \"Author Guidelines\", href: \"/\", external: false },\r\n      { name: \"News Archive\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Community\",\r\n    links: [\r\n      { name: \"Events\", href: \"/\", external: false },\r\n      { name: \"Reader Stories\", href: \"/\", external: false },\r\n      { name: \"Submit News\", href: \"/\", external: false },\r\n    ],\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAGO,MAAM,WAAuB;IAClC;QACE,MAAM;QACN,OAAO;QACP,MAAM,4SAAS;IACjB;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM,kUAAe;IACvB;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM,4SAAS;IACjB;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM,4SAAS;IACjB;CACD;AACM,MAAM,YAAyB;IACpC;QACE,OAAO;QACP,OAAO;YACL;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,0SAAO;gBACb,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,yTAAY;gBAClB,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,8XAA+B;gBACrC,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,4SAAS;gBACf,UAAU;YACZ;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,MAAM,qTAAY;YACpB;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,MAAM,oPAAa;YACrB;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,MAAM,sPAAe;YACvB;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,MAAM,oPAAa;YACrB;SACD;IACH;CACD;AACM,MAAM,cAA6B;IACxC;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;YAClD;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;YAClD;gBAAE,MAAM;gBAAkB,MAAM;gBAAK,UAAU;YAAM;SACtD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAK,UAAU;YAAM;YAC/C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,UAAU;YAAM;YAC9C;gBAAE,MAAM;gBAAS,MAAM;gBAAK,UAAU;YAAM;YAC5C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,UAAU;YAAM;SAC/C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAqB,MAAM;gBAAK,UAAU;YAAM;YACxD;gBAAE,MAAM;gBAAa,MAAM;gBAAK,UAAU;YAAM;YAChD;gBAAE,MAAM;gBAAmB,MAAM;gBAAK,UAAU;YAAM;SACvD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAc,MAAM;gBAAK,UAAU;YAAM;YACjD;gBAAE,MAAM;gBAAc,MAAM;gBAAK,UAAU;YAAM;YACjD;gBAAE,MAAM;gBAAa,MAAM;gBAAK,UAAU;YAAM;YAChD;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;SACnD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAgB,MAAM;gBAAK,UAAU;YAAM;YACnD;gBAAE,MAAM;gBAAkB,MAAM;gBAAK,UAAU;YAAM;YACrD;gBAAE,MAAM;gBAAiB,MAAM;gBAAK,UAAU;YAAM;YACpD;gBAAE,MAAM;gBAAoB,MAAM;gBAAK,UAAU;YAAM;SACxD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAiB,MAAM;gBAAK,UAAU;YAAM;YACpD;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;YAClD;gBAAE,MAAM;gBAAwB,MAAM;gBAAK,UAAU;YAAM;SAC5D;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAK,UAAU;YAAK;YAC9C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,UAAU;YAAK;YAC7C;gBAAE,MAAM;gBAAa,MAAM;gBAAK,UAAU;YAAK;YAC/C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,UAAU;YAAK;SAC9C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAK,UAAU;YAAM;YAC/C;gBAAE,MAAM;gBAAY,MAAM;gBAAK,UAAU;YAAM;YAC/C;gBAAE,MAAM;gBAAc,MAAM;gBAAK,UAAU;YAAM;YACjD;gBAAE,MAAM;gBAAU,MAAM;gBAAK,UAAU;YAAM;SAC9C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAmB,MAAM;gBAAK,UAAU;YAAM;YACtD;gBAAE,MAAM;gBAAqB,MAAM;gBAAK,UAAU;YAAM;YACxD;gBAAE,MAAM;gBAAgB,MAAM;gBAAK,UAAU;YAAM;SACpD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAU,MAAM;gBAAK,UAAU;YAAM;YAC7C;gBAAE,MAAM;gBAAkB,MAAM;gBAAK,UAAU;YAAM;YACrD;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;SACnD;IACH;CACD", "debugId": null}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/main-nav.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  NavigationMenu,\r\n  NavigationMenuContent,\r\n  NavigationMenuItem,\r\n  NavigationMenuLink,\r\n  NavigationMenuList,\r\n  NavigationMenuTrigger,\r\n} from '@/components/ui/navigation-menu';\r\nimport { navItems, newsLinks } from '@/config/docs';\r\nimport { cn } from '@/lib/utils';\r\nimport type { TNewsLink } from '@/types';\r\n\r\nexport function MainNav({ className, ...props }: React.ComponentProps<'nav'>) {\r\n  const pathname = usePathname();\r\n\r\n  return (\r\n    <nav className={cn('items-center gap-0.5', className)} {...props}>\r\n      <NavigationMenu>\r\n        <NavigationMenuList>\r\n          <NavigationMenuItem>\r\n            <NavigationMenuTrigger\r\n              aria-label=\"Open blog links\"\r\n              className=\"flex h-7 cursor-pointer items-center rounded-full bg-transparent px-3 py-4 font-normal text-md text-primary/80 transition-all duration-300 hover:bg-accent hover:text-primary dark:hover:bg-accent/50\"\r\n            >\r\n              Features\r\n            </NavigationMenuTrigger>\r\n            <NavigationMenuContent className=\"bg-background\">\r\n              <ul className=\"grid w-[500px] grid-cols-2 gap-6 p-4 lg:w-[600px]\">\r\n                {newsLinks.map((link: TNewsLink) => (\r\n                  <li className=\"flex flex-col gap-5\" key={link.title}>\r\n                    <p className=\"font-medium tracking-tighter\">{link.title}</p>\r\n                    <div className=\"flex flex-col gap-6\">\r\n                      {link.links.map((li) => (\r\n                        <NavigationMenuLink\r\n                          asChild\r\n                          className=\"group p-0 hover:bg-transparent\"\r\n                          key={li.title}\r\n                        >\r\n                          <Link href={li.href}>\r\n                            <div className=\"flex items-center gap-3\">\r\n                              <div className=\"rounded-sm border border-border p-2 transition-all duration-300 group-hover:bg-black dark:group-hover:bg-white\">\r\n                                <li.icon className=\"block size-4 transition-all duration-300 group-hover:text-white dark:group-hover:text-black\" />\r\n                              </div>\r\n\r\n                              <div className=\"flex flex-col gap-1\">\r\n                                <div className=\"font-medium text-sm leading-none\">\r\n                                  {li.title}\r\n                                </div>\r\n                                <p className=\"text-muted-foreground text-xs\">\r\n                                  View recent articles\r\n                                </p>\r\n                              </div>\r\n                            </div>\r\n                          </Link>\r\n                        </NavigationMenuLink>\r\n                      ))}\r\n                    </div>\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            </NavigationMenuContent>\r\n          </NavigationMenuItem>\r\n        </NavigationMenuList>\r\n      </NavigationMenu>\r\n      {navItems.map((item) => (\r\n        <Button\r\n          asChild\r\n          className=\"rounded-full\"\r\n          key={item.href}\r\n          size=\"sm\"\r\n          variant=\"ghost\"\r\n        >\r\n          <Link\r\n            className={cn(\r\n              'font-normal text-md text-primary/80 transition-all duration-300 hover:text-primary',\r\n              pathname === item.href && 'text-primary'\r\n            )}\r\n            href={item.href}\r\n          >\r\n            {item.label}\r\n          </Link>\r\n        </Button>\r\n      ))}\r\n    </nav>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAQA;AACA;;;AAdA;;;;;;;AAiBO,SAAS,QAAQ,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;;IACtB,MAAM,WAAW,IAAA,mRAAW;IAE5B,qBACE,4TAAC;QAAI,WAAW,IAAA,qHAAE,EAAC,wBAAwB;QAAa,GAAG,KAAK;;0BAC9D,4TAAC,4JAAc;0BACb,cAAA,4TAAC,gKAAkB;8BACjB,cAAA,4TAAC,gKAAkB;;0CACjB,4TAAC,mKAAqB;gCACpB,cAAW;gCACX,WAAU;0CACX;;;;;;0CAGD,4TAAC,mKAAqB;gCAAC,WAAU;0CAC/B,cAAA,4TAAC;oCAAG,WAAU;8CACX,8HAAS,CAAC,GAAG,CAAC,CAAC,qBACd,4TAAC;4CAAG,WAAU;;8DACZ,4TAAC;oDAAE,WAAU;8DAAgC,KAAK,KAAK;;;;;;8DACvD,4TAAC;oDAAI,WAAU;8DACZ,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,mBACf,4TAAC,gKAAkB;4DACjB,OAAO;4DACP,WAAU;sEAGV,cAAA,4TAAC,ySAAI;gEAAC,MAAM,GAAG,IAAI;0EACjB,cAAA,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;4EAAI,WAAU;sFACb,cAAA,4TAAC,GAAG,IAAI;gFAAC,WAAU;;;;;;;;;;;sFAGrB,4TAAC;4EAAI,WAAU;;8FACb,4TAAC;oFAAI,WAAU;8FACZ,GAAG,KAAK;;;;;;8FAEX,4TAAC;oFAAE,WAAU;8FAAgC;;;;;;;;;;;;;;;;;;;;;;;2DAZ9C,GAAG,KAAK;;;;;;;;;;;2CAPoB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAmC9D,6HAAQ,CAAC,GAAG,CAAC,CAAC,qBACb,4TAAC,wIAAM;oBACL,OAAO;oBACP,WAAU;oBAEV,MAAK;oBACL,SAAQ;8BAER,cAAA,4TAAC,ySAAI;wBACH,WAAW,IAAA,qHAAE,EACX,sFACA,aAAa,KAAK,IAAI,IAAI;wBAE5B,MAAM,KAAK,IAAI;kCAEd,KAAK,KAAK;;;;;;mBAXR,KAAK,IAAI;;;;;;;;;;;AAiBxB;GAzEgB;;QACG,mRAAW;;;KADd", "debugId": null}}]}