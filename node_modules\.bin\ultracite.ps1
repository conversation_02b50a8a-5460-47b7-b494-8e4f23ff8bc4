#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="C:\Users\<USER>\Documents\nProjects\better\node_modules\.pnpm\ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee\node_modules\ultracite\dist\node_modules;C:\Users\<USER>\Documents\nProjects\better\node_modules\.pnpm\ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee\node_modules\ultracite\node_modules;C:\Users\<USER>\Documents\nProjects\better\node_modules\.pnpm\ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee\node_modules;C:\Users\<USER>\Documents\nProjects\better\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/c/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee/node_modules/ultracite/dist/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee/node_modules/ultracite/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../.pnpm/ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee/node_modules/ultracite/dist/index.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../.pnpm/ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee/node_modules/ultracite/dist/index.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../.pnpm/ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee/node_modules/ultracite/dist/index.js" $args
  } else {
    & "node$exe"  "$basedir/../.pnpm/ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee/node_modules/ultracite/dist/index.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
