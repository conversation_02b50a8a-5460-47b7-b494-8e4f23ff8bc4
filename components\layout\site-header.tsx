import { ArmchairIcon } from 'lucide-react';
import Link from 'next/link';
import { But<PERSON> } from '../ui/button';
import { MainNav } from './main-nav';

export default function SiteHeader() {
  return (
    <header className="fixed top-4 right-0 left-0 z-50 flex w-full translate-y-0 justify-center opacity-100 transition-all duration-500 ease-out">
      <nav className="relative mx-2 flex h-[60px] w-full max-w-6xl select-none items-center justify-between bg-transparent px-2.5 py-2 transition-all duration-200 ease-in-out sm:mx-4 xl:grid xl:grid-cols-3 xl:gap-8">
        <div className="flex items-center gap-4 xl:justify-start">
          <div className="flex translate-y-0 items-center gap-1.5 px-3 opacity-100 transition-all delay-75 duration-500 ease-out">
            <Link className="flex items-center gap-1.5" href="/">
              <ArmchairIcon className="block size-5" />
              <span className="pb-[1.5px] font-medium text-lg">
                Better Flow
              </span>
            </Link>
          </div>
        </div>
        <MainNav className="hidden lg:flex" />
        <div className="flex items-center justify-end gap-2 xl:justify-end">
          <Button className="rounded-full bg-[#ebff0a]" size={'lg'}>
            Book Demo
          </Button>
        </div>
      </nav>
    </header>
  );
}
