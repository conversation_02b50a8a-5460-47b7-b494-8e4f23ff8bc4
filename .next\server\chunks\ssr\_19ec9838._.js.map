{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/footer-cta.tsx"], "sourcesContent": ["export default function FooterCta() {\r\n  return <div>FooterCta</div>;\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBAAO,6WAAC;kBAAI;;;;;;AACd", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/site-footer.tsx"], "sourcesContent": ["export default function SiteFooter() {\r\n  return <div>SiteFooter</div>;\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBAAO,6WAAC;kBAAI;;;;;;AACd", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,oOAAO,EAAC,IAAA,8LAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,kPAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,6SAAI,GAAG;IAE9B,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/main-nav.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const MainNav = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainNav() from the server but MainNav is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/main-nav.tsx <module evaluation>\",\n    \"MainNav\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,UAAU,IAAA,uYAAuB,EAC1C;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,gEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/main-nav.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const MainNav = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainNav() from the server but MainNav is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/main-nav.tsx\",\n    \"MainNav\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,UAAU,IAAA,uYAAuB,EAC1C;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,4CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/site-header.tsx"], "sourcesContent": ["import { ArmchairIcon } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport { But<PERSON> } from '../ui/button';\r\nimport { MainNav } from './main-nav';\r\n\r\nexport default function SiteHeader() {\r\n  return (\r\n    <header className=\"fixed top-4 right-0 left-0 z-50 flex w-full translate-y-0 justify-center opacity-100 transition-all duration-500 ease-out\">\r\n      <nav className=\"relative mx-2 flex h-[60px] w-full max-w-6xl select-none items-center justify-between bg-transparent px-2.5 py-2 transition-all duration-200 ease-in-out sm:mx-4 xl:grid xl:grid-cols-3 xl:gap-8\">\r\n        <div className=\"flex items-center gap-4 xl:justify-start\">\r\n          <div className=\"flex translate-y-0 items-center gap-1.5 px-3 opacity-100 transition-all delay-75 duration-500 ease-out\">\r\n            <Link className=\"flex items-center gap-1.5\" href=\"/\">\r\n              <ArmchairIcon className=\"block size-5\" />\r\n              <span className=\"pb-[1.5px] font-medium text-lg\">\r\n                Better Flow\r\n              </span>\r\n            </Link>\r\n          </div>\r\n        </div>\r\n        <MainNav className=\"hidden lg:flex\" />\r\n        <div className=\"flex items-center justify-end gap-2 xl:justify-end\">\r\n          <Button className=\"rounded-full bg-[#ebff0a]\" size={'lg'}>\r\n            Book Demo\r\n          </Button>\r\n        </div>\r\n      </nav>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,6WAAC;QAAO,WAAU;kBAChB,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC,sSAAI;4BAAC,WAAU;4BAA4B,MAAK;;8CAC/C,6WAAC,kTAAY;oCAAC,WAAU;;;;;;8CACxB,6WAAC;oCAAK,WAAU;8CAAiC;;;;;;;;;;;;;;;;;;;;;;8BAMvD,6WAAC,+IAAO;oBAAC,WAAU;;;;;;8BACnB,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC,qIAAM;wBAAC,WAAU;wBAA4B,MAAM;kCAAM;;;;;;;;;;;;;;;;;;;;;;AAOpE", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/app/%28root%29/layout.tsx"], "sourcesContent": ["import FooterCta from '@/components/layout/footer-cta';\r\nimport SiteFooter from '@/components/layout/site-footer';\r\nimport SiteHeader from '@/components/layout/site-header';\r\n\r\nexport default function AppLayout({ children }: { children: React.ReactNode }) {\r\n  return (\r\n    <div className=\"container-wrapper dark relative z-10 flex min-h-svh flex-col bg-[#090909] text-white\">\r\n      <SiteHeader />\r\n      <main className=\"container relative flex flex-1 flex-col\">\r\n        {children}\r\n      </main>\r\n      <FooterCta />\r\n      <SiteFooter />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEe,SAAS,UAAU,EAAE,QAAQ,EAAiC;IAC3E,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC,kJAAU;;;;;0BACX,6WAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,6WAAC,iJAAS;;;;;0BACV,6WAAC,kJAAU;;;;;;;;;;;AAGjB", "debugId": null}}]}