'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import { navItems, newsLinks } from '@/config/docs';
import { cn } from '@/lib/utils';
import type { TNewsLink } from '@/types';

export function MainNav({ className, ...props }: React.ComponentProps<'nav'>) {
  const pathname = usePathname();

  return (
    <nav className={cn('items-center gap-0.5', className)} {...props}>
      <NavigationMenu>
        <NavigationMenuList>
          <NavigationMenuItem>
            <NavigationMenuTrigger
              aria-label="Open blog links"
              className="flex h-7 cursor-pointer items-center rounded-full bg-transparent px-3 py-4 font-normal text-md text-primary/80 transition-all duration-300 hover:bg-accent hover:text-primary dark:hover:bg-accent/50"
            >
              Features
            </NavigationMenuTrigger>
            <NavigationMenuContent className="bg-background">
              <ul className="grid w-[500px] grid-cols-2 gap-6 p-4 lg:w-[600px]">
                {newsLinks.map((link: TNewsLink) => (
                  <li className="flex flex-col gap-5" key={link.title}>
                    <p className="font-medium tracking-tighter">{link.title}</p>
                    <div className="flex flex-col gap-6">
                      {link.links.map((li) => (
                        <NavigationMenuLink
                          asChild
                          className="group p-0 hover:bg-transparent"
                          key={li.title}
                        >
                          <Link href={li.href}>
                            <div className="flex items-center gap-3">
                              <div className="rounded-sm border border-border p-2 transition-all duration-300 group-hover:bg-black dark:group-hover:bg-white">
                                <li.icon className="block size-4 transition-all duration-300 group-hover:text-white dark:group-hover:text-black" />
                              </div>

                              <div className="flex flex-col gap-1">
                                <div className="font-medium text-sm leading-none">
                                  {li.title}
                                </div>
                                <p className="text-muted-foreground text-xs">
                                  View recent articles
                                </p>
                              </div>
                            </div>
                          </Link>
                        </NavigationMenuLink>
                      ))}
                    </div>
                  </li>
                ))}
              </ul>
            </NavigationMenuContent>
          </NavigationMenuItem>
        </NavigationMenuList>
      </NavigationMenu>
      {navItems.map((item) => (
        <Button
          asChild
          className="rounded-full"
          key={item.href}
          size="sm"
          variant="ghost"
        >
          <Link
            className={cn(
              'font-normal text-md text-primary/80 transition-all duration-300 hover:text-primary',
              pathname === item.href && 'text-primary'
            )}
            href={item.href}
          >
            {item.label}
          </Link>
        </Button>
      ))}
    </nav>
  );
}
