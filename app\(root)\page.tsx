export default function Home() {
  return (
    <div className="flex flex-col gap-6 py-20 lg:py-48">
      <div className="font-sans">
        <h2 className="font-bold">Sans</h2>
        <p>
          Are you looking for inspiration to create a stunning portfolio website
          that stands out in the given landing page examples? Crafting a
          captivating online portfolio can be an intimidating task, especially
          when you're trying to find the right template. There is a way to
          simplify the process and find a NextJS portfolio template tailored to
          your needs. Imagine having a solution that allows you to jumpstart
          your digital presence and showcase your skills seamlessly.
        </p>
      </div>
      <div className="font-mono">
        <h2 className="font-bold">Mono</h2>
        <p>
          Are you looking for inspiration to create a stunning portfolio website
          that stands out in the given landing page examples? Crafting a
          captivating online portfolio can be an intimidating task, especially
          when you're trying to find the right template. There is a way to
          simplify the process and find a NextJS portfolio template tailored to
          your needs. Imagine having a solution that allows you to jumpstart
          your digital presence and showcase your skills seamlessly.
        </p>
      </div>
      <div className="font-aeonik">
        <h2 className="font-bold">Aeonik</h2>
        <p>
          Are you looking for inspiration to create a stunning portfolio website
          that stands out in the given landing page examples? Crafting a
          captivating online portfolio can be an intimidating task, especially
          when you're trying to find the right template. There is a way to
          simplify the process and find a NextJS portfolio template tailored to
          your needs. Imagine having a solution that allows you to jumpstart
          your digital presence and showcase your skills seamlessly.
        </p>
      </div>
      <div className="font-aeonik-bold">
        <h2 className="font-bold">Aeonik Bold</h2>
        <p>
          Are you looking for inspiration to create a stunning portfolio website
          that stands out in the given landing page examples? Crafting a
          captivating online portfolio can be an intimidating task, especially
          when you're trying to find the right template. There is a way to
          simplify the process and find a NextJS portfolio template tailored to
          your needs. Imagine having a solution that allows you to jumpstart
          your digital presence and showcase your skills seamlessly.
        </p>
      </div>
    </div>
  );
}
