#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee/node_modules/ultracite/dist/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee/node_modules/ultracite/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee/node_modules/ultracite/dist/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee/node_modules/ultracite/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee/node_modules/ultracite/dist/index.js" "$@"
else
  exec node  "$basedir/../.pnpm/ultracite@5.2.9_@types+node_da5c484c75048d06aaea6dbf3e8704ee/node_modules/ultracite/dist/index.js" "$@"
fi
