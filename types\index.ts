import type { LucideProps } from "lucide-react";
import type { ForwardRefExoticComponent, RefAttributes } from "react";

export type TNavItem = {
  href: string;
  label: string;
  icon: ForwardRefExoticComponent<
    Omit<LucideProps, "ref"> & RefAttributes<SVGSVGElement>
  >;
};
export type TNewsLink = {
  title: string;
  links: {
    title: string;
    href: string;
    icon: ForwardRefExoticComponent<
      Omit<LucideProps, "ref"> & RefAttributes<SVGSVGElement>
    >;
    external: boolean;
  }[];
};

export type TFooterLink = {
  title: string;
  links: {
    name: string;
    href: string;
    external: boolean;
  }[];
};
